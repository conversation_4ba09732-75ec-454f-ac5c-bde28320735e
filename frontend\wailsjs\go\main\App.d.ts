// Cynhyrchwyd y ffeil hon yn awtomatig. PEIDIWCH Â MODIWL
// This file is automatically generated. DO NOT EDIT
import {models} from '../models';

export function AddPatient(arg1:string):Promise<void>;

export function AutoCollapseAfterInactivity():Promise<void>;

export function ClearPatientList():Promise<void>;

export function GenerateQRCode():Promise<string>;

export function GenerateRegistrationQRCode():Promise<Record<string, string>>;

export function GetConfig():Promise<models.AppConfig>;

export function GetCurrentRegistrationNumber():Promise<number>;

export function GetModeConfig():Promise<Record<string, models.ModeInfo>>;

export function GetPatientList():Promise<Array<models.Patient>>;

export function GetRegistrations():Promise<Array<models.Registration>>;

export function GetSiteInfo():Promise<models.SiteInfo>;

export function GetTodayPatientCount():Promise<number>;

export function GetWindowState():Promise<Record<string, any>>;

export function Greet(arg1:string):Promise<string>;

export function HandleHotkey(arg1:string):Promise<void>;

export function HandleKeyboardShortcut(arg1:string):Promise<void>;

export function MinimizeWindow():Promise<void>;

export function ProcessScreenshotAndUpload(arg1:string,arg2:string):Promise<string>;

export function RemovePatient(arg1:number):Promise<void>;

export function SetAlwaysOnTop(arg1:boolean):Promise<void>;

export function SetWindowPosition(arg1:string):Promise<void>;

export function ToggleWindowSize():Promise<void>;

export function UpdateCropSettings(arg1:models.AppConfig):Promise<void>;

export function UpdateSiteInfo(arg1:models.AppConfig):Promise<void>;
