// 云对象：hc-screenshot-records
// 用于管理检测截图记录
// 云对象教程: https://uniapp.dcloud.net.cn/uniCloud/cloud-obj

module.exports = {

    /**
     * 创建或更新截图记录
     * @param {Object} params - 参数对象
     * @param {string} params.user_name - 用户姓名
     * @param {string} params.user_id - 用户ID
     * @param {string} params.site_id - 网点ID
     * @param {string} params.device_no - 设备MAC地址
     * @param {string} params.registration_id - 挂号记录ID
     * @param {number} params.round - 轮次 1-10
     * @param {string} params.analysis_type - 分析类型 A01/B02/C03
     * @param {string} params.detected_organ - 识别的器官名称
     * @param {string} params.filename - 文件名
     * @param {string} params.cloud_url - 云存储URL
     * @param {string} params.operator_name - 操作员姓名
     * @param {boolean} params.ocr_success - OCR是否成功
     * @returns {Promise<Object>} 包含操作结果的对象
     */
    async createOrUpdateScreenshotRecord(params = {}) {
        const dbJQL = uniCloud.databaseForJQL({
            clientInfo: this.getClientInfo()
        });

        console.log('[hc-screenshot-records][createOrUpdateScreenshotRecord] 收到参数:', JSON.stringify(params));

        try {
            // 对于URL化的云对象，需要检查是否是HTTP请求
            let actualParams = params;
            
            // 获取HTTP信息进行调试
            try {
                const httpInfo = this.getHttpInfo();
                console.log('[createOrUpdateScreenshotRecord] HTTP信息:', JSON.stringify({
                    method: httpInfo?.method,
                    queryStringParameters: httpInfo?.queryStringParameters,
                    body: httpInfo?.body
                }));
                
                // 如果是HTTP POST请求，尝试从body获取
                if (httpInfo && httpInfo.body) {
                    try {
                        const bodyData = JSON.parse(httpInfo.body);
                        actualParams = { ...actualParams, ...bodyData };
                        console.log('[createOrUpdateScreenshotRecord] 合并POST参数后:', JSON.stringify(actualParams));
                    } catch (parseError) {
                        console.log('[createOrUpdateScreenshotRecord] 解析HTTP body失败:', parseError.message);
                    }
                }
            } catch (httpError) {
                console.log('[createOrUpdateScreenshotRecord] 获取HTTP信息失败，使用原始参数');
            }

            const {
                user_name,
                user_id,
                site_id,
                device_no,
                registration_id,
                round,
                analysis_type,
                detected_organ,
                filename,
                cloud_url,
                operator_name,
                ocr_success
            } = actualParams;

            // 参数验证
            if (!user_name || !analysis_type || !filename) {
                return {
                    errCode: "PARAM_ERROR",
                    errMsg: "缺少必要参数: user_name, analysis_type, filename"
                };
            }

            // 生成session_id
            const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
            const session_id = `SES_${user_name}_${today}`;
            
            // 生成report_number
            const report_number = `RPT${today}001`;

            // 设置操作角色为admin，跳过schema权限验证
            await dbJQL.setUser({
                role: ['admin']
            });

            // 查找是否已存在该用户今天的记录
            const existingRecord = await dbJQL.collection('hc-detection-screenshot-records')
                .where({
                    session_id: session_id
                })
                .get();

            const currentTime = new Date();
            
            if (existingRecord.data.length === 0) {
                // 创建新记录
                console.log('[createOrUpdateScreenshotRecord] 创建新的截图记录');
                
                const newRecord = {
                    session_id: session_id,
                    user_id: user_id || "unknown_user",
                    user_name: user_name,
                    site_id: site_id || "",
                    device_no: device_no || "",
                    registration_id: registration_id || "",
                    report_number: report_number,
                    current_round: round || 1,
                    total_rounds: 10,
                    screenshots: [
                        this.createScreenshotEntry(round || 1, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success)
                    ],
                    session_status: "CREATED",
                    operator_name: operator_name || "系统操作员",
                    session_start_time: currentTime,
                    created_time: currentTime,
                    updated_time: currentTime
                };

                const result = await dbJQL.collection('hc-detection-screenshot-records').add(newRecord);
                
                if (result.id) {
                    return {
                        errCode: 0,
                        errMsg: "截图记录创建成功",
                        data: {
                            action: "created",
                            session_id: session_id,
                            record_id: result.id,
                            current_round: round || 1
                        }
                    };
                } else {
                    return {
                        errCode: 'ADD_FAILED',
                        errMsg: '截图记录创建失败，未创建任何记录',
                        result: result
                    };
                }
                
            } else {
                // 更新现有记录
                console.log('[createOrUpdateScreenshotRecord] 更新现有截图记录');
                
                const record = existingRecord.data[0];
                const screenshots = record.screenshots || [];
                
                // 查找当前轮次的截图记录
                let currentRoundScreenshot = screenshots.find(s => s.round === (round || 1));
                
                if (!currentRoundScreenshot) {
                    // 添加新轮次的截图记录
                    currentRoundScreenshot = this.createScreenshotEntry(round || 1, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success);
                    screenshots.push(currentRoundScreenshot);
                } else {
                    // 更新现有轮次的截图记录
                    this.updateScreenshotEntry(currentRoundScreenshot, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success);
                }

                // 计算会话状态
                const newStatus = this.calculateSessionStatus(round || 1, analysis_type);
                const newCurrentRound = Math.max(record.current_round || 1, round || 1);

                // 更新记录
                const updateData = {
                    screenshots: screenshots,
                    current_round: newCurrentRound,
                    session_status: newStatus,
                    updated_time: currentTime
                };

                // 如果是最后一轮，设置结束时间
                if (newStatus === "COMPLETED") {
                    updateData.session_end_time = currentTime;
                }

                const updateResult = await dbJQL.collection('hc-detection-screenshot-records').doc(record._id).update(updateData);
                
                return {
                    errCode: 0,
                    errMsg: "截图记录更新成功",
                    data: {
                        action: "updated",
                        session_id: session_id,
                        record_id: record._id,
                        current_round: newCurrentRound,
                        session_status: newStatus,
                        affectedDocs: updateResult.affectedDocs
                    }
                };
            }
            
        } catch (e) {
            console.error('[hc-screenshot-records][createOrUpdateScreenshotRecord] 处理截图记录时出错:', e);
            
            let errMsg = '数据库操作失败。';
            if (e.message) {
                errMsg = e.message;
            }
            if (e.code) {
                errMsg = `数据库错误: ${e.code} - ${e.message}`;
            }
            // 特别处理 TOKEN_INVALID_ANONYMOUS_USER 错误
            if (e.code === 'TOKEN_INVALID_ANONYMOUS_USER' || (e.errMsg && e.errMsg.includes('TOKEN_INVALID_ANONYMOUS_USER'))) {
                errMsg = '用户未登录或登录状态已失效，请重新登录后再试。';
            }
            
            return {
                errCode: e.code || 'DATABASE_ERROR',
                errMsg: errMsg
            };
        }
    },

    /**
     * 查询用户检测记录
     * @param {Object} params - 参数对象
     * @param {string} params.user_id - 用户ID
     * @param {string} params.user_name - 用户姓名
     * @param {string} params.site_id - 网点ID
     * @param {string} params.device_no - 设备MAC地址
     * @param {string} params.date - 日期 YYYY-MM-DD
     * @param {number} params.page - 页码
     * @param {number} params.pageSize - 每页数量
     * @returns {Promise<Object>} 包含查询结果的对象
     */
    async getSessionsByUser(params = {}) {
        const dbJQL = uniCloud.databaseForJQL({
            clientInfo: this.getClientInfo()
        });

        console.log('[hc-screenshot-records][getSessionsByUser] 收到参数:', JSON.stringify(params));

        try {
            const {
                user_id,
                user_name,
                site_id,
                device_no,
                date,
                page = 1,
                pageSize = 20
            } = params;

            let whereCondition = {};
            
            if (user_id) whereCondition.user_id = user_id;
            if (user_name) whereCondition.user_name = user_name;
            if (site_id) whereCondition.site_id = site_id;
            if (device_no) whereCondition.device_no = device_no;
            
            // 如果指定了日期，添加日期过滤
            if (date) {
                const sessionIdPattern = `SES_.*_${date.replace(/-/g, '')}`;
                whereCondition.session_id = new RegExp(sessionIdPattern);
            }

            // 设置操作角色为admin
            await dbJQL.setUser({
                role: ['admin']
            });

            const result = await dbJQL.collection('hc-detection-screenshot-records')
                .where(whereCondition)
                .orderBy('created_time', 'desc')
                .skip((page - 1) * pageSize)
                .limit(pageSize)
                .get();

            // 统计总数
            const countResult = await dbJQL.collection('hc-detection-screenshot-records')
                .where(whereCondition)
                .count();

            return {
                errCode: 0,
                errMsg: "查询成功",
                data: result.data,
                total: countResult.total,
                hasMore: countResult.total > page * pageSize
            };
            
        } catch (e) {
            console.error('[hc-screenshot-records][getSessionsByUser] 查询检测记录时出错:', e);
            
            let errMsg = '数据库操作失败。';
            if (e.message) {
                errMsg = e.message;
            }
            if (e.code) {
                errMsg = `数据库错误: ${e.code} - ${e.message}`;
            }
            
            return {
                errCode: e.code || 'DATABASE_ERROR',
                errMsg: errMsg
            };
        }
    },

    // 创建截图条目
    createScreenshotEntry(round, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success) {
        const entry = {
            round: round,
            detected_organ: detected_organ || "未知器官",
            screenshot_time: currentTime,
            operator_name: operator_name || "系统操作员",
            ocr_success: ocr_success || false,
            coze_workflow_called: false,
            notes: ""
        };

        // 根据分析类型设置对应的文件名和URL
        if (analysis_type === "A01") {
            entry.analysis_type1 = "A01";
            entry.A01_filename = filename;
            entry.A01_screenshot_cloud_url = cloud_url;
        } else if (analysis_type === "B02") {
            entry.analysis_type1 = "B02";
            entry.B02_filename = filename;
            entry.B02_screenshot_cloud_url = cloud_url;
        } else if (analysis_type === "C03") {
            entry.analysis_type2 = "C03";
            entry.C03_filename = filename;
            entry.C03_screenshot_cloud_url = cloud_url;
        }

        return entry;
    },

    // 更新截图条目
    updateScreenshotEntry(entry, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success) {
        // 更新通用字段
        if (detected_organ && detected_organ !== "未知器官") {
            entry.detected_organ = detected_organ;
        }
        entry.screenshot_time = currentTime;
        entry.operator_name = operator_name || entry.operator_name;
        entry.ocr_success = ocr_success || entry.ocr_success;

        // 根据分析类型更新对应的文件名和URL
        if (analysis_type === "A01") {
            entry.analysis_type1 = "A01";
            entry.A01_filename = filename;
            entry.A01_screenshot_cloud_url = cloud_url;
        } else if (analysis_type === "B02") {
            entry.analysis_type1 = "B02";
            entry.B02_filename = filename;
            entry.B02_screenshot_cloud_url = cloud_url;
        } else if (analysis_type === "C03") {
            entry.analysis_type2 = "C03";
            entry.C03_filename = filename;
            entry.C03_screenshot_cloud_url = cloud_url;
        }
    },

    // 计算会话状态
    calculateSessionStatus(round, analysis_type) {
        if (round === 1 && analysis_type === "B02") {
            return "CREATED";
        } else if (round === 10 && analysis_type === "C03") {
            return "COMPLETED";
        } else {
            return "IN_PROGRESS";
        }
    }

};
