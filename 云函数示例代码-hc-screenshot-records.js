// 云函数：hc-screenshot-records/createOrUpdate
// 用于创建或更新截图记录

'use strict';

const db = uniCloud.database();
const collection = db.collection('hc-detection-screenshot-records');

exports.main = async (event, context) => {
    console.log('收到截图记录请求:', event);
    
    try {
        const {
            user_name,
            user_id,
            site_id,
            device_no,
            registration_id,
            round,
            analysis_type,
            detected_organ,
            filename,
            cloud_url,
            operator_name,
            ocr_success
        } = event;

        // 参数验证
        if (!user_name || !analysis_type || !filename) {
            return {
                errCode: "400",
                errMsg: "缺少必要参数: user_name, analysis_type, filename"
            };
        }

        // 生成session_id
        const today = new Date().toISOString().slice(0, 10).replace(/-/g, '');
        const session_id = `SES_${user_name}_${today}`;
        
        // 生成report_number
        const report_number = `RPT${today}001`;

        // 查找是否已存在该用户今天的记录
        const existingRecord = await collection
            .where({
                session_id: session_id
            })
            .get();

        const currentTime = new Date();
        
        if (existingRecord.data.length === 0) {
            // 创建新记录
            console.log('创建新的截图记录');
            
            const newRecord = {
                session_id: session_id,
                user_id: user_id || "unknown_user",
                user_name: user_name,
                site_id: site_id || "",
                device_no: device_no || "",
                registration_id: registration_id || "",
                report_number: report_number,
                current_round: round || 1,
                total_rounds: 10,
                screenshots: [
                    createScreenshotEntry(round || 1, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success)
                ],
                session_status: "CREATED",
                operator_name: operator_name || "系统操作员",
                session_start_time: currentTime,
                created_time: currentTime,
                updated_time: currentTime
            };

            const result = await collection.add(newRecord);
            
            return {
                errCode: "0",
                errMsg: "success",
                data: {
                    action: "created",
                    session_id: session_id,
                    record_id: result.id,
                    current_round: round || 1
                }
            };
            
        } else {
            // 更新现有记录
            console.log('更新现有截图记录');
            
            const record = existingRecord.data[0];
            const screenshots = record.screenshots || [];
            
            // 查找当前轮次的截图记录
            let currentRoundScreenshot = screenshots.find(s => s.round === (round || 1));
            
            if (!currentRoundScreenshot) {
                // 添加新轮次的截图记录
                currentRoundScreenshot = createScreenshotEntry(round || 1, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success);
                screenshots.push(currentRoundScreenshot);
            } else {
                // 更新现有轮次的截图记录
                updateScreenshotEntry(currentRoundScreenshot, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success);
            }

            // 计算会话状态
            const newStatus = calculateSessionStatus(round || 1, analysis_type);
            const newCurrentRound = Math.max(record.current_round || 1, round || 1);

            // 更新记录
            const updateData = {
                screenshots: screenshots,
                current_round: newCurrentRound,
                session_status: newStatus,
                updated_time: currentTime
            };

            // 如果是最后一轮，设置结束时间
            if (newStatus === "COMPLETED") {
                updateData.session_end_time = currentTime;
            }

            await collection.doc(record._id).update(updateData);
            
            return {
                errCode: "0",
                errMsg: "success",
                data: {
                    action: "updated",
                    session_id: session_id,
                    record_id: record._id,
                    current_round: newCurrentRound,
                    session_status: newStatus
                }
            };
        }
        
    } catch (error) {
        console.error('处理截图记录时出错:', error);
        return {
            errCode: "500",
            errMsg: "服务器内部错误: " + error.message
        };
    }
};

// 创建截图条目
function createScreenshotEntry(round, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success) {
    const entry = {
        round: round,
        detected_organ: detected_organ || "未知器官",
        screenshot_time: currentTime,
        operator_name: operator_name || "系统操作员",
        ocr_success: ocr_success || false,
        coze_workflow_called: false,
        notes: ""
    };

    // 根据分析类型设置对应的文件名和URL
    if (analysis_type === "A01") {
        entry.analysis_type1 = "A01";
        entry.A01_filename = filename;
        entry.A01_screenshot_cloud_url = cloud_url;
    } else if (analysis_type === "B02") {
        entry.analysis_type1 = "B02";
        entry.B02_filename = filename;
        entry.B02_screenshot_cloud_url = cloud_url;
    } else if (analysis_type === "C03") {
        entry.analysis_type2 = "C03";
        entry.C03_filename = filename;
        entry.C03_screenshot_cloud_url = cloud_url;
    }

    return entry;
}

// 更新截图条目
function updateScreenshotEntry(entry, analysis_type, detected_organ, filename, cloud_url, currentTime, operator_name, ocr_success) {
    // 更新通用字段
    if (detected_organ && detected_organ !== "未知器官") {
        entry.detected_organ = detected_organ;
    }
    entry.screenshot_time = currentTime;
    entry.operator_name = operator_name || entry.operator_name;
    entry.ocr_success = ocr_success || entry.ocr_success;

    // 根据分析类型更新对应的文件名和URL
    if (analysis_type === "A01") {
        entry.analysis_type1 = "A01";
        entry.A01_filename = filename;
        entry.A01_screenshot_cloud_url = cloud_url;
    } else if (analysis_type === "B02") {
        entry.analysis_type1 = "B02";
        entry.B02_filename = filename;
        entry.B02_screenshot_cloud_url = cloud_url;
    } else if (analysis_type === "C03") {
        entry.analysis_type2 = "C03";
        entry.C03_filename = filename;
        entry.C03_screenshot_cloud_url = cloud_url;
    }
}

// 计算会话状态
function calculateSessionStatus(round, analysis_type) {
    if (round === 1 && analysis_type === "B02") {
        return "CREATED";
    } else if (round === 10 && analysis_type === "C03") {
        return "COMPLETED";
    } else {
        return "IN_PROGRESS";
    }
}

// 查询用户检测记录的云函数
// 云函数：hc-screenshot-records/getSessionsByUser
exports.getSessionsByUser = async (event, context) => {
    try {
        const {
            user_id,
            user_name,
            site_id,
            device_no,
            date,
            page = 1,
            pageSize = 20
        } = event;

        let whereCondition = {};
        
        if (user_id) whereCondition.user_id = user_id;
        if (user_name) whereCondition.user_name = user_name;
        if (site_id) whereCondition.site_id = site_id;
        if (device_no) whereCondition.device_no = device_no;
        
        // 如果指定了日期，添加日期过滤
        if (date) {
            const sessionIdPattern = `SES_.*_${date.replace(/-/g, '')}`;
            whereCondition.session_id = new RegExp(sessionIdPattern);
        }

        const result = await collection
            .where(whereCondition)
            .orderBy('created_time', 'desc')
            .skip((page - 1) * pageSize)
            .limit(pageSize)
            .get();

        // 统计总数
        const countResult = await collection
            .where(whereCondition)
            .count();

        return {
            errCode: "0",
            errMsg: "success",
            data: result.data,
            total: countResult.total
        };
        
    } catch (error) {
        console.error('查询检测记录时出错:', error);
        return {
            errCode: "500",
            errMsg: "服务器内部错误: " + error.message
        };
    }
};
