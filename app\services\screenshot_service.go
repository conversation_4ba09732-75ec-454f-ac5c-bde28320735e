package services

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"os"
	"path/filepath"
	"time"

	"github.com/kbinani/screenshot"
)

// ScreenshotService 截图服务
type ScreenshotService struct {
	configService *ConfigService
}

// NewScreenshotService 创建新的截图服务
func NewScreenshotService(configService *ConfigService) *ScreenshotService {
	return &ScreenshotService{
		configService: configService,
	}
}

// TakeScreenshot 截取屏幕截图
func (ss *ScreenshotService) TakeScreenshot(mode string, userName string) (string, error) {
	// 获取屏幕截图
	bounds := screenshot.GetDisplayBounds(0)
	img, err := screenshot.CaptureRect(bounds)
	if err != nil {
		return "", fmt.Errorf("截图失败: %v", err)
	}

	// 获取配置
	config := ss.configService.GetConfig()
	if config == nil {
		return "", fmt.Errorf("配置未加载")
	}

	// 裁剪图像
	croppedImg := ss.cropImage(img, config.CropSettings)

	// 图像预处理
	processedImg := ss.preprocessImage(croppedImg)

	// 生成文件名
	modeInfo := ss.configService.GetModeConfig()[mode]
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("%s-%s-%s.png", userName, modeInfo.Code, timestamp)
	filePath := filepath.Join("pic", fileName)

	// 确保目录存在
	if err := os.MkdirAll("pic", 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}

	// 保存图像
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	if err := png.Encode(file, processedImg); err != nil {
		return "", fmt.Errorf("保存图像失败: %v", err)
	}

	return filePath, nil
}

// cropImage 裁剪图像
func (ss *ScreenshotService) cropImage(img image.Image, cropSettings struct {
	TopPercent    float64 `json:"top_percent"`
	BottomPercent float64 `json:"bottom_percent"`
	LeftPercent   float64 `json:"left_percent"`
	RightPercent  float64 `json:"right_percent"`
}) image.Image {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算裁剪区域
	left := int(float64(width) * cropSettings.LeftPercent)
	right := width - int(float64(width)*cropSettings.RightPercent)
	top := int(float64(height) * cropSettings.TopPercent)
	bottom := height - int(float64(height)*cropSettings.BottomPercent)

	// 创建裁剪后的图像
	croppedBounds := image.Rect(0, 0, right-left, bottom-top)
	croppedImg := image.NewRGBA(croppedBounds)

	for y := top; y < bottom; y++ {
		for x := left; x < right; x++ {
			croppedImg.Set(x-left, y-top, img.At(x, y))
		}
	}

	return croppedImg
}

// preprocessImage 图像预处理
func (ss *ScreenshotService) preprocessImage(img image.Image) image.Image {
	bounds := img.Bounds()
	enhancedImg := image.NewRGBA(bounds)

	// 1. 提高对比度和亮度
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			r, g, b, a := img.At(x, y).RGBA()

			// 将颜色值从uint32转换为float64进行处理
			rf := float64(r >> 8)
			gf := float64(g >> 8)
			bf := float64(b >> 8)

			// 提高对比度（增加20%）
			contrast := 1.2
			rf = ((rf - 128.0) * contrast) + 128.0
			gf = ((gf - 128.0) * contrast) + 128.0
			bf = ((bf - 128.0) * contrast) + 128.0

			// 确保值在0-255范围内
			rf = ss.maxf(0.0, ss.minf(255.0, rf))
			gf = ss.maxf(0.0, ss.minf(255.0, gf))
			bf = ss.maxf(0.0, ss.minf(255.0, bf))

			enhancedImg.Set(x, y, color.RGBA{
				R: uint8(rf),
				G: uint8(gf),
				B: uint8(bf),
				A: uint8(a >> 8),
			})
		}
	}

	// 2. 轻微锐化处理
	sharpenKernel := []float64{
		0, -0.5, 0,
		-0.5, 3, -0.5,
		0, -0.5, 0,
	}

	sharpenedImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64

			// 应用锐化核
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := enhancedImg.At(x+kx, y+ky).RGBA()
					weight := sharpenKernel[(ky+1)*3+(kx+1)]
					sumR += float64(r>>8) * weight
					sumG += float64(g>>8) * weight
					sumB += float64(b>>8) * weight
				}
			}

			// 确保值在0-255范围内
			r := uint8(ss.maxf(0.0, ss.minf(255.0, sumR)))
			g := uint8(ss.maxf(0.0, ss.minf(255.0, sumG)))
			b := uint8(ss.maxf(0.0, ss.minf(255.0, sumB)))

			sharpenedImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	// 3. 轻微平滑处理（使用简单的3x3均值滤波）
	finalImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64
			count := 0.0

			// 3x3邻域平均
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := sharpenedImg.At(x+kx, y+ky).RGBA()
					sumR += float64(r >> 8)
					sumG += float64(g >> 8)
					sumB += float64(b >> 8)
					count++
				}
			}

			// 计算平均值
			r := uint8(ss.maxf(0.0, ss.minf(255.0, sumR/count)))
			g := uint8(ss.maxf(0.0, ss.minf(255.0, sumG/count)))
			b := uint8(ss.maxf(0.0, ss.minf(255.0, sumB/count)))

			finalImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	return finalImg
}

// 辅助函数
func (ss *ScreenshotService) maxf(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

func (ss *ScreenshotService) minf(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}
