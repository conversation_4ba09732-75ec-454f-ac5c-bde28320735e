package services

import (
	"fmt"
	"image"
	"image/color"
	"image/png"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/kbinani/screenshot"
)

// ScreenshotService 截图服务
type ScreenshotService struct {
	configService *ConfigService
	ocrService    OCRInterface
}

// NewScreenshotService 创建新的截图服务
func NewScreenshotService(configService *ConfigService) *ScreenshotService {
	return &ScreenshotService{
		configService: configService,
		ocrService:    NewOCRService(),
	}
}

// TakeScreenshot 截取屏幕截图
func (ss *ScreenshotService) TakeScreenshot(mode string, userName string) (string, error) {
	// 获取屏幕截图
	bounds := screenshot.GetDisplayBounds(0)
	img, err := screenshot.CaptureRect(bounds)
	if err != nil {
		return "", fmt.Errorf("截图失败: %v", err)
	}

	// 获取配置
	config := ss.configService.GetConfig()
	if config == nil {
		return "", fmt.Errorf("配置未加载")
	}

	// 裁剪图像
	croppedImg := ss.cropImage(img, config.CropSettings)

	// 图像预处理
	processedImg := ss.preprocessImage(croppedImg)

	// 生成文件名
	modeInfo := ss.configService.GetModeConfig()[mode]
	timestamp := time.Now().Format("20060102_150405")
	fileName := fmt.Sprintf("%s-%s-%s.png", userName, modeInfo.Code, timestamp)
	filePath := filepath.Join("pic", fileName)

	// 确保目录存在
	if err := os.MkdirAll("pic", 0755); err != nil {
		return "", fmt.Errorf("创建目录失败: %v", err)
	}

	// 保存图像
	file, err := os.Create(filePath)
	if err != nil {
		return "", fmt.Errorf("创建文件失败: %v", err)
	}
	defer file.Close()

	if err := png.Encode(file, processedImg); err != nil {
		return "", fmt.Errorf("保存图像失败: %v", err)
	}

	return filePath, nil
}

// TakeScreenshotWithOCRAsync 截取屏幕截图并异步进行OCR识别
// 这个方法保持原有截图功能不变，同时启动异步OCR识别
func (ss *ScreenshotService) TakeScreenshotWithOCRAsync(mode string, userName string) (string, error) {
	// 1. 首先执行原有的截图功能（保持不变）
	filePath, err := ss.TakeScreenshot(mode, userName)
	if err != nil {
		return "", err
	}

	// 2. 异步启动OCR识别（不影响主流程）
	go ss.performAsyncOCRAnalysis(filePath, mode, userName)

	return filePath, nil
}

// performAsyncOCRAnalysis 异步执行OCR分析
func (ss *ScreenshotService) performAsyncOCRAnalysis(originalFilePath, mode, userName string) {
	// 使用专门的OCR区域截图方法
	ocrFilePath, organName, err := ss.TakeOCRRegionScreenshot(mode, userName)
	if err != nil {
		fmt.Printf("OCR区域截图失败: %v\n", err)
		return
	}

	// 记录OCR识别结果
	fmt.Printf("OCR识别完成 - 原始文件: %s, OCR文件: %s, 器官: %s\n", originalFilePath, ocrFilePath, organName)

	// 这里可以将OCR结果保存到数据库或发送到前端
	// TODO: 将OCR结果保存到数据库或通知前端
}

// TakeOCRRegionScreenshot 直接截取屏幕左上角区域用于OCR识别
// 这个方法专门用于OCR，只截取左上角1/4区域
func (ss *ScreenshotService) TakeOCRRegionScreenshot(mode string, userName string) (string, string, error) {
	// 获取屏幕截图
	bounds := screenshot.GetDisplayBounds(0)

	// 计算左上角1/4区域
	ocrWidth := bounds.Dx() / 4
	ocrHeight := bounds.Dy() / 4
	ocrBounds := image.Rect(bounds.Min.X, bounds.Min.Y, bounds.Min.X+ocrWidth, bounds.Min.Y+ocrHeight)

	// 直接截取左上角区域
	ocrImg, err := screenshot.CaptureRect(ocrBounds)
	if err != nil {
		return "", "", fmt.Errorf("OCR区域截图失败: %v", err)
	}

	// 对OCR图像进行预处理以提高识别率
	processedOCRImg := ss.preprocessImageForOCR(ocrImg)

	// 生成OCR文件名
	modeInfo := ss.configService.GetModeConfig()[mode]
	timestamp := time.Now().Format("20060102_150405")
	ocrFileName := fmt.Sprintf("%s-%s-OCR-%s.png", userName, modeInfo.Code, timestamp)
	ocrFilePath := filepath.Join("pic", ocrFileName)

	// 确保目录存在
	if err := os.MkdirAll("pic", 0755); err != nil {
		return "", "", fmt.Errorf("创建目录失败: %v", err)
	}

	// 保存OCR区域图像
	ocrFile, err := os.Create(ocrFilePath)
	if err != nil {
		return "", "", fmt.Errorf("创建OCR文件失败: %v", err)
	}
	defer ocrFile.Close()

	if err := png.Encode(ocrFile, processedOCRImg); err != nil {
		return "", "", fmt.Errorf("保存OCR图像失败: %v", err)
	}

	// 使用OCR识别器官名称
	organName, err := ss.extractOrganFromTableFirstRow(ocrFilePath)
	if err != nil {
		// OCR识别失败时返回默认值，但不报错
		organName = "未知器官"
		fmt.Printf("OCR识别失败: %v\n", err)
	}

	return ocrFilePath, organName, nil
}

// createOCRRegionScreenshot 从完整截图中创建OCR专用的区域截图并识别器官名称
func (ss *ScreenshotService) createOCRRegionScreenshot(img image.Image, userName, modeCode, timestamp string) (string, string, error) {
	// 截取左上角区域（1/4宽度，1/4高度）
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算OCR区域：左上角1/4区域
	ocrWidth := width / 4
	ocrHeight := height / 4

	// 创建OCR区域图像
	ocrBounds := image.Rect(0, 0, ocrWidth, ocrHeight)
	ocrImg := image.NewRGBA(ocrBounds)

	// 复制左上角区域
	for y := 0; y < ocrHeight; y++ {
		for x := 0; x < ocrWidth; x++ {
			ocrImg.Set(x, y, img.At(x, y))
		}
	}

	// 对OCR图像进行预处理以提高识别率
	processedOCRImg := ss.preprocessImageForOCR(ocrImg)

	// 保存OCR区域图像
	ocrFileName := fmt.Sprintf("%s-%s-OCR-%s.png", userName, modeCode, timestamp)
	ocrFilePath := filepath.Join("pic", ocrFileName)

	// 确保目录存在
	if err := os.MkdirAll("pic", 0755); err != nil {
		return "", "", fmt.Errorf("创建目录失败: %v", err)
	}

	ocrFile, err := os.Create(ocrFilePath)
	if err != nil {
		return "", "", fmt.Errorf("创建OCR文件失败: %v", err)
	}
	defer ocrFile.Close()

	if err := png.Encode(ocrFile, processedOCRImg); err != nil {
		return "", "", fmt.Errorf("保存OCR图像失败: %v", err)
	}

	// 使用OCR识别器官名称
	organName, err := ss.extractOrganFromTableFirstRow(ocrFilePath)
	if err != nil {
		return ocrFilePath, "未知器官", fmt.Errorf("OCR识别失败: %v", err)
	}

	return ocrFilePath, organName, nil
}

// cropImage 裁剪图像
func (ss *ScreenshotService) cropImage(img image.Image, cropSettings struct {
	TopPercent    float64 `json:"top_percent"`
	BottomPercent float64 `json:"bottom_percent"`
	LeftPercent   float64 `json:"left_percent"`
	RightPercent  float64 `json:"right_percent"`
}) image.Image {
	bounds := img.Bounds()
	width := bounds.Dx()
	height := bounds.Dy()

	// 计算裁剪区域
	left := int(float64(width) * cropSettings.LeftPercent)
	right := width - int(float64(width)*cropSettings.RightPercent)
	top := int(float64(height) * cropSettings.TopPercent)
	bottom := height - int(float64(height)*cropSettings.BottomPercent)

	// 创建裁剪后的图像
	croppedBounds := image.Rect(0, 0, right-left, bottom-top)
	croppedImg := image.NewRGBA(croppedBounds)

	for y := top; y < bottom; y++ {
		for x := left; x < right; x++ {
			croppedImg.Set(x-left, y-top, img.At(x, y))
		}
	}

	return croppedImg
}

// preprocessImage 图像预处理
func (ss *ScreenshotService) preprocessImage(img image.Image) image.Image {
	bounds := img.Bounds()
	enhancedImg := image.NewRGBA(bounds)

	// 1. 提高对比度和亮度
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			r, g, b, a := img.At(x, y).RGBA()

			// 将颜色值从uint32转换为float64进行处理
			rf := float64(r >> 8)
			gf := float64(g >> 8)
			bf := float64(b >> 8)

			// 提高对比度（增加20%）
			contrast := 1.2
			rf = ((rf - 128.0) * contrast) + 128.0
			gf = ((gf - 128.0) * contrast) + 128.0
			bf = ((bf - 128.0) * contrast) + 128.0

			// 确保值在0-255范围内
			rf = ss.maxf(0.0, ss.minf(255.0, rf))
			gf = ss.maxf(0.0, ss.minf(255.0, gf))
			bf = ss.maxf(0.0, ss.minf(255.0, bf))

			enhancedImg.Set(x, y, color.RGBA{
				R: uint8(rf),
				G: uint8(gf),
				B: uint8(bf),
				A: uint8(a >> 8),
			})
		}
	}

	// 2. 轻微锐化处理
	sharpenKernel := []float64{
		0, -0.5, 0,
		-0.5, 3, -0.5,
		0, -0.5, 0,
	}

	sharpenedImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64

			// 应用锐化核
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := enhancedImg.At(x+kx, y+ky).RGBA()
					weight := sharpenKernel[(ky+1)*3+(kx+1)]
					sumR += float64(r>>8) * weight
					sumG += float64(g>>8) * weight
					sumB += float64(b>>8) * weight
				}
			}

			// 确保值在0-255范围内
			r := uint8(ss.maxf(0.0, ss.minf(255.0, sumR)))
			g := uint8(ss.maxf(0.0, ss.minf(255.0, sumG)))
			b := uint8(ss.maxf(0.0, ss.minf(255.0, sumB)))

			sharpenedImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	// 3. 轻微平滑处理（使用简单的3x3均值滤波）
	finalImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y + 1; y < bounds.Max.Y-1; y++ {
		for x := bounds.Min.X + 1; x < bounds.Max.X-1; x++ {
			var sumR, sumG, sumB float64
			count := 0.0

			// 3x3邻域平均
			for ky := -1; ky <= 1; ky++ {
				for kx := -1; kx <= 1; kx++ {
					r, g, b, _ := sharpenedImg.At(x+kx, y+ky).RGBA()
					sumR += float64(r >> 8)
					sumG += float64(g >> 8)
					sumB += float64(b >> 8)
					count++
				}
			}

			// 计算平均值
			r := uint8(ss.maxf(0.0, ss.minf(255.0, sumR/count)))
			g := uint8(ss.maxf(0.0, ss.minf(255.0, sumG/count)))
			b := uint8(ss.maxf(0.0, ss.minf(255.0, sumB/count)))

			finalImg.Set(x, y, color.RGBA{r, g, b, 255})
		}
	}

	return finalImg
}

// 辅助函数
func (ss *ScreenshotService) maxf(a, b float64) float64 {
	if a > b {
		return a
	}
	return b
}

func (ss *ScreenshotService) minf(a, b float64) float64 {
	if a < b {
		return a
	}
	return b
}

// preprocessImageForOCR OCR专用图像预处理
func (ss *ScreenshotService) preprocessImageForOCR(img image.Image) image.Image {
	bounds := img.Bounds()

	// 1. 转换为灰度图像以提高OCR识别率
	grayImg := image.NewGray(bounds)
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			r, g, b, _ := img.At(x, y).RGBA()
			// 使用标准灰度转换公式
			gray := uint8((299*r + 587*g + 114*b) / 1000 >> 8)
			grayImg.Set(x, y, color.Gray{Y: gray})
		}
	}

	// 2. 增强对比度
	enhancedImg := image.NewGray(bounds)
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			gray := grayImg.GrayAt(x, y).Y
			// 增强对比度
			enhanced := float64(gray)
			enhanced = (enhanced-128.0)*1.5 + 128.0
			enhanced = ss.maxf(0.0, ss.minf(255.0, enhanced))
			enhancedImg.Set(x, y, color.Gray{Y: uint8(enhanced)})
		}
	}

	// 3. 转换回RGBA格式
	finalImg := image.NewRGBA(bounds)
	for y := bounds.Min.Y; y < bounds.Max.Y; y++ {
		for x := bounds.Min.X; x < bounds.Max.X; x++ {
			gray := enhancedImg.GrayAt(x, y).Y
			finalImg.Set(x, y, color.RGBA{R: gray, G: gray, B: gray, A: 255})
		}
	}

	return finalImg
}

// extractOrganFromTableFirstRow 从表格第一行提取器官名称
func (ss *ScreenshotService) extractOrganFromTableFirstRow(imagePath string) (string, error) {
	if ss.ocrService == nil {
		return "未知器官", fmt.Errorf("OCR服务未初始化")
	}

	// 使用OCR提取文字
	fullText, err := ss.ocrService.ExtractTextFromImage(imagePath)
	if err != nil {
		return "未知器官", fmt.Errorf("OCR文字提取失败: %v", err)
	}

	// 解析表格第一行的器官名称
	organName := ss.parseOrganFromTableFirstRow(fullText)
	if organName == "" {
		return "未知器官", fmt.Errorf("未能从表格中识别器官名称")
	}

	return organName, nil
}

// parseOrganFromTableFirstRow 解析表格第一行的器官名称
func (ss *ScreenshotService) parseOrganFromTableFirstRow(text string) string {
	lines := strings.Split(text, "\n")

	for _, line := range lines {
		line = strings.TrimSpace(line)
		if line == "" {
			continue
		}

		// 查找包含"0.000"的行（表格第一行的标志）
		if strings.Contains(line, "0.000") {
			// 分割行内容，寻找器官名称
			parts := strings.Fields(line)

			// 查找"0.000"后面的内容
			for i, part := range parts {
				if strings.Contains(part, "0.000") && i+1 < len(parts) {
					// 下一个字段应该是器官名称
					organCandidate := parts[i+1]

					// 验证是否为有效的器官名称
					if ss.isValidOrganName(organCandidate) {
						return ss.cleanOrganName(organCandidate)
					}
				}
			}

			// 如果上面的方法没找到，尝试正则表达式匹配
			return ss.extractOrganWithRegexFromLine(line)
		}
	}

	// 如果没有找到包含"0.000"的行，尝试从第一行提取
	if len(lines) > 0 {
		firstLine := strings.TrimSpace(lines[0])
		if firstLine != "" {
			return ss.extractOrganWithRegexFromLine(firstLine)
		}
	}

	return ""
}

// isValidOrganName 验证是否为有效的器官名称
func (ss *ScreenshotService) isValidOrganName(name string) bool {
	// 清理名称
	cleanName := ss.cleanOrganName(name)

	// 器官关键词列表
	organKeywords := []string{
		"心脏", "肝脏", "肺", "肾脏", "脾脏", "胃", "肠", "胆囊", "胰腺",
		"甲状腺", "前列腺", "子宫", "卵巢", "乳腺", "膀胱", "食管",
		"十二指肠", "小肠", "大肠", "结肠", "直肠", "阑尾",
		"心", "肝", "脾", "肺", "肾", "胆", "胰", "胃肠", "肠胃",
		"头部", "颈部", "胸部", "腹部", "盆腔", "四肢",
		"脑", "脑部", "头颅", "颅脑", "脊柱", "脊椎",
		"骨骼", "关节", "肌肉", "血管", "神经",
	}

	// 检查是否包含器官关键词
	for _, keyword := range organKeywords {
		if strings.Contains(cleanName, keyword) {
			return true
		}
	}

	// 检查长度和字符（中文器官名称通常2-6个字符）
	if len(cleanName) >= 2 && len(cleanName) <= 18 { // 考虑UTF-8编码
		// 简单检查是否包含中文字符
		for _, r := range cleanName {
			if r >= 0x4e00 && r <= 0x9fff {
				return true
			}
		}
	}

	return false
}

// cleanOrganName 清理器官名称
func (ss *ScreenshotService) cleanOrganName(name string) string {
	// 移除常见的非器官字符
	name = strings.TrimSpace(name)
	name = strings.Trim(name, "()[]{}\"'.,;:!?")

	// 移除数字和特殊符号
	cleaned := ""
	for _, r := range name {
		if (r >= 0x4e00 && r <= 0x9fff) || // 中文字符
			(r >= 'a' && r <= 'z') || // 小写字母
			(r >= 'A' && r <= 'Z') { // 大写字母
			cleaned += string(r)
		}
	}

	return cleaned
}

// extractOrganWithRegexFromLine 使用正则表达式从行中提取器官名称
func (ss *ScreenshotService) extractOrganWithRegexFromLine(line string) string {
	// 由于regexp导入问题，先使用简单的字符串匹配
	// 常用的器官名称关键词
	organKeywords := []string{
		"心脏", "肝脏", "肺", "肾脏", "脾脏", "胃", "肠", "胆囊", "胰腺",
		"甲状腺", "前列腺", "子宫", "卵巢", "乳腺", "膀胱", "食管",
		"十二指肠", "小肠", "大肠", "结肠", "直肠", "阑尾",
		"心", "肝", "脾", "肺", "肾", "胆", "胰", "胃肠", "肠胃",
		"头部", "颈部", "胸部", "腹部", "盆腔", "四肢",
		"脑", "脑部", "头颅", "颅脑", "脊柱", "脊椎",
		"骨骼", "关节", "肌肉", "血管", "神经",
	}

	// 查找包含器官关键词的部分
	for _, keyword := range organKeywords {
		if strings.Contains(line, keyword) {
			// 找到关键词的位置，提取周围的文字
			index := strings.Index(line, keyword)
			if index >= 0 {
				// 提取包含关键词的词汇
				start := index
				end := index + len(keyword)

				// 向前扩展，包含可能的修饰词
				for start > 0 && ss.isChineseChar(rune(line[start-1])) {
					start--
				}

				// 向后扩展，包含可能的修饰词
				for end < len(line) && ss.isChineseChar(rune(line[end])) {
					end++
				}

				candidate := line[start:end]
				if ss.isValidOrganName(candidate) {
					return ss.cleanOrganName(candidate)
				}
			}
		}
	}

	return ""
}

// isChineseChar 检查是否为中文字符
func (ss *ScreenshotService) isChineseChar(r rune) bool {
	return r >= 0x4e00 && r <= 0x9fff
}
