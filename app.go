package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"MagneticOperator/app/models"
	"MagneticOperator/app/services"
	"MagneticOperator/app/utils"

	"github.com/wailsapp/wails/v2/pkg/runtime"
)

// App struct
type App struct {
	ctx context.Context

	// 服务实例
	configService     *services.ConfigService
	screenshotService *services.ScreenshotService
	qrcodeService     *services.QRCodeService
	apiService        *services.APIService
	patientService    *services.PatientService
	hotkeyService     *services.HotkeyService
	ocrService        services.OCRInterface

	// 当前配置
	config *models.AppConfig

	// 窗体状态
	isExpanded     bool
	windowPosition string // "left" 或 "right"
}

// NewApp creates a new App application struct
func NewApp() *App {
	return &App{}
}

// startup is called when the app starts. The context is saved
// so we can call the runtime methods
func (a *App) startup(ctx context.Context) {
	fmt.Println("=== 应用开始启动 ===")
	a.ctx = ctx

	// 初始化日志
	if err := utils.InitLogger(); err != nil {
		fmt.Printf("警告: 初始化日志失败: %v\n", err)
		return
	}

	fmt.Println("日志系统初始化成功")
	utils.LogInfo("日志系统初始化成功")

	// 初始化服务
	a.initServices()

	// 初始化窗体状态
	a.isExpanded = false
	a.windowPosition = "left" // 默认左侧

	// 设置初始窗体位置
	a.setWindowToSide()

	// 创建必要的目录
	if err := os.MkdirAll("pic", 0755); err != nil {
		fmt.Printf("创建pic目录失败: %v\n", err)
		utils.LogError("创建目录失败", "系统", err)
	}
	if err := os.MkdirAll("config", 0755); err != nil {
		fmt.Printf("创建config目录失败: %v\n", err)
		utils.LogError("创建目录失败", "系统", err)
	}

	// 启动快捷键监听
	if a.hotkeyService != nil {
		a.hotkeyService.StartHotkeyListener()
		utils.LogInfo("快捷键服务启动成功")
	}

	fmt.Println("=== 应用启动成功 ===")
	utils.LogInfo("应用启动成功")
}

// initServices 初始化所有服务
func (a *App) initServices() {
	utils.LogInfo("开始初始化服务...")

	// 创建配置服务
	a.configService = services.NewConfigService(a.ctx)

	// 加载配置
	config, err := a.configService.LoadConfig()
	if err != nil {
		utils.LogError("加载配置失败", "系统", err)
		// 使用默认配置
		a.config = &models.AppConfig{}
		a.setDefaultConfig()
		utils.LogInfo("已加载默认配置")
	} else {
		a.config = config
		utils.LogInfo("成功加载配置文件")
	}

	// 创建其他服务
	a.screenshotService = services.NewScreenshotService(a.configService)
	a.qrcodeService = services.NewQRCodeService(a.configService)
	a.apiService = services.NewAPIService(a.configService)
	a.patientService = services.NewPatientService(a.configService, a.apiService)
	a.hotkeyService = services.NewHotkeyService(a)
	a.ocrService = services.NewOCRService()

	// 设置API服务到配置服务，用于后续加载站点信息
	a.configService.SetAPIService(a.apiService)

	// 从API加载站点信息并检查是否有变化
	utils.LogInfo("开始从API加载站点信息...")
	if err := a.configService.LoadSiteInfoFromAPIWithCache(false); err != nil {
		utils.LogError("从API加载站点信息失败", "系统", err)
		// 这里可以根据需要处理错误，例如使用本地配置的站点信息或提示用户
	} else {
		// 站点信息加载成功后，重新获取更新后的配置
		a.config = a.configService.GetConfig()
		if a.configService.IsSiteInfoChanged() {
			utils.LogInfo("站点信息有变化，将重新生成二维码")
			fmt.Printf("站点信息更新成功: %s (%s)\n", a.config.SiteInfo.SiteName, a.config.SiteInfo.SiteID)
		} else {
			utils.LogInfo("站点信息无变化，复用现有二维码")
			fmt.Printf("站点信息无变化: %s (%s)\n", a.config.SiteInfo.SiteName, a.config.SiteInfo.SiteID)
		}
	}
}

// setDefaultConfig 设置默认配置
func (a *App) setDefaultConfig() {
	a.config.CropSettings.TopPercent = 0.153
	a.config.CropSettings.BottomPercent = 0.051
	a.config.CropSettings.LeftPercent = 0.0482
	a.config.CropSettings.RightPercent = 0.30
}

// GetConfig 获取应用配置
func (a *App) GetConfig() *models.AppConfig {
	// 只在调试模式下输出详细日志，避免日志冗余
	if a.config.SiteInfo.SiteID != "" {
		utils.LogInfo(fmt.Sprintf("GetConfig called - SiteInfo: %s (%s)", a.config.SiteInfo.SiteName, a.config.SiteInfo.SiteID))
	} else {
		utils.LogInfo("GetConfig called - SiteInfo not loaded yet")
	}
	return a.config
}

// UpdateSiteInfo 更新网点信息
func (a *App) UpdateSiteInfo(siteInfo models.AppConfig) error {
	// 站点信息现在从API动态加载，不再支持手动更新
	utils.LogOperation("尝试手动更新网点信息，但此功能已改为API自动更新", "系统用户", siteInfo.SiteInfo.SiteID)

	// 触发重新从API加载站点信息
	if err := a.configService.LoadSiteInfoFromAPI(); err != nil {
		return fmt.Errorf("从API重新加载站点信息失败: %v", err)
	}

	// 更新当前配置
	a.config = a.configService.GetConfig()
	utils.LogInfo("站点信息已从API重新加载")

	return nil
}

// UpdateCropSettings 更新裁剪设置
func (a *App) UpdateCropSettings(cropSettings models.AppConfig) error {
	utils.LogOperation("更新裁剪设置", "系统用户", a.config.SiteInfo.SiteID)
	return a.configService.UpdateCropSettings(cropSettings)
}

// ProcessScreenshotAndUpload 处理截图并上传的完整流程
func (a *App) ProcessScreenshotAndUpload(mode string, userName string) (string, error) {
	// 获取当前选中的用户信息
	registrations, err := a.apiService.GetRegistrations()
	if err != nil {
		return "", fmt.Errorf("获取用户信息失败: %v", err)
	}

	// 默认用户信息（如果没有选中用户）
	var currentUser *models.Registration
	if len(registrations) > 0 {
		// 使用第一个用户作为当前用户（实际应用中应该从前端传递选中的用户索引）
		currentUser = &registrations[0]
	}
	utils.LogOperation("处理截图并上传", userName, a.config.SiteInfo.SiteID)
	fmt.Printf("[DEBUG] 开始处理截图并上传流程 - 模式: %s, 用户: %s\n", mode, userName)

	// 1. 截取屏幕截图并进行OCR识别以获取器官名称
	fmt.Printf("[DEBUG] 步骤1: 开始截取屏幕截图并识别器官名称...\n")

	// 1.1 先进行OCR区域截图识别器官名称
	fmt.Printf("[DEBUG] 步骤1.1: OCR识别器官名称...\n")
	_, organName, err := a.screenshotService.TakeOCRRegionScreenshot(mode, userName)
	if err != nil {
		fmt.Printf("[DEBUG] 步骤1.1: OCR识别失败，使用默认器官名称 - %v\n", err)
		organName = "未知器官"
	} else {
		fmt.Printf("[DEBUG] 步骤1.1成功: 识别到器官名称 - %s\n", organName)
	}

	// 1.2 使用识别的器官名称进行优化命名的截图
	fmt.Printf("[DEBUG] 步骤1.2: 使用优化命名进行截图...\n")
	filePath, err := a.screenshotService.TakeScreenshotWithOptimizedNaming(mode, userName, organName, currentUser)
	if err != nil {
		utils.LogError("截取屏幕截图失败", userName, err)
		fmt.Printf("[ERROR] 步骤1.2失败: 截取屏幕截图失败 - %v\n", err)
		return "", fmt.Errorf("截取屏幕截图失败: %v", err)
	}
	fmt.Printf("[DEBUG] 步骤1.2成功: 截图已保存到 %s\n", filePath)

	// 2. 上传图片到DCloud
	fmt.Printf("[DEBUG] 步骤2: 开始上传图片到DCloud...\n")
	picURL, err := a.apiService.UploadImageToDCloud(filePath)
	if err != nil {
		utils.LogError("上传图片失败", userName, err)
		fmt.Printf("[ERROR] 步骤2失败: 上传图片失败 - %v\n", err)
		return "", fmt.Errorf("上传图片失败: %v", err)
	}
	fmt.Printf("[DEBUG] 步骤2成功: 图片已上传，URL: %s\n", picURL)

	// 3. 调用扣子工作流
	fmt.Printf("[DEBUG] 步骤3: 开始调用扣子工作流...\n")
	modeInfo := a.configService.GetModeConfig()[mode]
	reportID := fmt.Sprintf("%s-%s", userName, modeInfo.Code)
	// fmt.Printf("[DEBUG] 扣子工作流参数 - 图片URL: %s, 文件路径: %s, 报告ID: %s\n", picURL, filePath, reportID)

	// 使用真实用户信息构建CozeUserInput
	userInput := services.CozeUserInput{
		UserID:         "unknown_user",
		Gender:         0,
		BirthDate:      "1990-01-01",
		RegistrationID: "unknown_registration",
	}
	fmt.Printf("[DEBUG] currentUser: %s", currentUser)
	// fmt.Printf("[DEBUG] userInput: %s\n", userInput)

	// 如果有当前用户信息，使用真实数据
	if currentUser != nil {
		userInput.UserID = currentUser.UserID
		userInput.RegistrationID = currentUser.RegistrationNumber

		// 如果有用户详细信息，使用第一个用户信息
		if len(currentUser.UserInfo) > 0 {
			userInfo := currentUser.UserInfo[0]
			// userInput.BirthDate = userInfo.BirthDate
			userInput.Gender = userInfo.Gender
		}
	}

	if err := a.apiService.CallCozeWorkflow(picURL, filePath, reportID, userInput); err != nil {
		utils.LogError("调用扣子工作流失败", userName, err)
		fmt.Printf("[ERROR] 步骤3失败: 调用扣子工作流失败 - %v\n", err)
		// 注意：这里不返回错误，因为图片已经成功上传
		utils.LogWarning(fmt.Sprintf("扣子工作流调用失败，但图片已上传: %s", picURL))
	} else {
		fmt.Printf("[DEBUG] 步骤3成功: 扣子工作流调用完成\n")
	}

	fmt.Printf("[DEBUG] 截图并上传流程完成，返回图片URL: %s\n", picURL)
	return picURL, nil
}

// UploadLatestScreenshot 上传最新的截图文件
// 注释原因：A、B、C三个截图快捷键都通过ProcessScreenshotAndUpload函数处理，此函数已不再需要
// func (a *App) UploadLatestScreenshot() (string, error) {
// 	utils.LogOperation("上传最新截图", "医生或健康专家", a.config.SiteInfo.SiteID)

// 	// 获取pic目录下最新的PNG文件
// 	files, err := filepath.Glob("pic/*.png")
// 	if err != nil || len(files) == 0 {
// 		return "", fmt.Errorf("未找到截图文件")
// 	}

// 	// 按修改时间排序，获取最新的文件
// 	latestFile := files[0]
// 	latestTime, err := os.Stat(latestFile)
// 	if err != nil {
// 		return "", fmt.Errorf("获取文件信息失败: %v", err)
// 	}

// 	for _, file := range files[1:] {
// 		fileInfo, err := os.Stat(file)
// 		if err != nil {
// 			continue
// 		}
// 		if fileInfo.ModTime().After(latestTime.ModTime()) {
// 			latestFile = file
// 			latestTime = fileInfo
// 		}
// 	}

// 	// 上传图片到DCloud
// 	picURL, err := a.apiService.UploadImageToDCloud(latestFile)
// 	if err != nil {
// 		utils.LogError("上传图片失败", "医生或健康专家", err)
// 		return "", fmt.Errorf("上传图片失败: %v", err)
// 	}

// 	// 调用扣子工作流
// 	reportID := fmt.Sprintf("医生或健康专家-%s", time.Now().Format("20060102_150405"))
// 	if err := a.apiService.CallCozeWorkflow(picURL, latestFile, reportID); err != nil {
// 		utils.LogError("调用扣子工作流失败", "医生或健康专家", err)
// 		// 注意：这里不返回错误，因为图片已经成功上传
// 		utils.LogWarning(fmt.Sprintf("扣子工作流调用失败，但图片已上传: %s", picURL))
// 	}

// 	return picURL, nil
// }

// GenerateQRCode 生成患者二维码 (旧版，可能需要调整或移除)
func (a *App) GenerateQRCode() (string, error) {
	utils.LogOperation("生成患者二维码", "系统用户", a.config.SiteInfo.SiteID)

	// 使用带缓存的二维码生成方法
	_, filePath, err := a.qrcodeService.GenerateCustomAppQRCodeWithCache() // 这实际上是生成报到二维码的逻辑
	if err != nil {
		utils.LogError("生成患者二维码失败", "系统用户", err)
		return "", err
	}

	// 记录是否使用了缓存
	if a.configService.IsSiteInfoChanged() {
		utils.LogInfo("重新生成了患者二维码")
	} else {
		utils.LogInfo("复用了缓存的患者二维码")
	}

	return filePath, nil
}

// GenerateRegistrationQRCode 生成报到二维码并返回Base64编码的图片和文件路径
func (a *App) GenerateRegistrationQRCode() (map[string]string, error) {
	utils.LogOperation("生成报到二维码", "系统用户", a.config.SiteInfo.SiteID)

	// 使用带缓存的二维码生成方法
	qrCodeBytes, filePath, err := a.qrcodeService.GenerateCustomAppQRCodeWithCache()
	if err != nil {
		utils.LogError("生成报到二维码失败", "系统用户", err)
		return nil, fmt.Errorf("生成报到二维码失败: %v", err)
	}

	// 将二维码图片字节转换为Base64字符串
	qrCodeBase64 := utils.EncodeBytesToBase64(qrCodeBytes)

	// 记录是否使用了缓存
	if a.configService.IsSiteInfoChanged() {
		utils.LogInfo("重新生成了报到二维码")
	} else {
		utils.LogInfo("复用了缓存的报到二维码")
	}

	return map[string]string{
		"qr_code_base64": qrCodeBase64,
		"file_path":      filePath,
	}, nil
}

// AddPatient 添加患者
func (a *App) AddPatient(name string) error {
	utils.LogOperation("添加患者", name, a.config.SiteInfo.SiteID)

	err := a.patientService.AddPatient(name)
	if err != nil {
		utils.LogError("添加患者", name, err)
	}

	return err
}

// GetPatientList 获取患者列表
func (a *App) GetPatientList() []models.Patient {
	return a.patientService.GetPatientList()
}

// GetSiteInfo 获取站点信息
func (a *App) GetSiteInfo() (*models.SiteInfo, error) {
	utils.LogInfo("GetSiteInfo called - 开始获取站点信息")

	// 优先使用缓存的站点信息，避免重复API调用
	// 如果缓存为空或需要强制更新，才调用API
	if a.config.SiteInfo.SiteID == "" || a.config.SiteInfo.SiteName == "" {
		utils.LogInfo("站点信息缓存为空，从API加载")
		err := a.configService.LoadSiteInfoFromAPIWithCache(false)
		if err != nil {
			utils.LogError("GetSiteInfo - 从API加载站点信息失败", "系统用户", err)
			return nil, fmt.Errorf("获取站点信息失败: %v", err)
		}
		// 更新当前配置
		a.config = a.configService.GetConfig()
	} else {
		utils.LogInfo("使用缓存的站点信息")
	}

	// 返回站点信息
	siteInfo := &a.config.SiteInfo
	utils.LogInfo(fmt.Sprintf("GetSiteInfo - 成功获取站点信息: ID=%s, Name=%s", siteInfo.SiteID, siteInfo.SiteName))
	return siteInfo, nil
}

// RemovePatient 移除患者
func (a *App) RemovePatient(index int) error {
	utils.LogOperation("移除患者", "系统用户", a.config.SiteInfo.SiteID)

	err := a.patientService.RemovePatient(index)
	if err != nil {
		utils.LogError("移除患者", "系统用户", err)
	}

	return err
}

// ClearPatientList 清空患者列表
func (a *App) ClearPatientList() {
	utils.LogOperation("清空患者列表", "系统用户", a.config.SiteInfo.SiteID)
	a.patientService.ClearPatientList()
}

// GetCurrentRegistrationNumber 获取当前挂号序号
func (a *App) GetCurrentRegistrationNumber() int {
	return a.patientService.GetCurrentRegistrationNumber()
}

// GetRegistrations 获取候检者列表
func (a *App) GetRegistrations() ([]models.Registration, error) {
	utils.LogOperation("获取候检者列表", "系统用户", a.config.SiteInfo.SiteID)

	registrations, err := a.apiService.GetRegistrations()
	if err != nil {
		utils.LogError("获取候检者列表", "系统用户", err)
		return nil, err
	}

	return registrations, nil
}

// GetTodayPatientCount 获取今日患者数量
func (a *App) GetTodayPatientCount() int {
	return a.patientService.GetTodayPatientCount()
}

// GetModeConfig 获取模式配置
func (a *App) GetModeConfig() map[string]models.ModeInfo {
	return a.configService.GetModeConfig()
}

// ToggleWindowSize 切换窗体大小
func (a *App) ToggleWindowSize() {
	if a.isExpanded {
		// 收缩为小窗体
		runtime.WindowSetSize(a.ctx, 380, 800)
		a.setWindowToSide()
	} else {
		// 扩展为大窗体
		runtime.WindowSetSize(a.ctx, 1200, 800)
		runtime.WindowCenter(a.ctx)
	}
	a.isExpanded = !a.isExpanded
	utils.LogOperation("切换窗体大小", "系统用户", fmt.Sprintf("扩展状态: %v", a.isExpanded))
}

// SetWindowPosition 设置窗体位置偏好
func (a *App) SetWindowPosition(position string) {
	a.windowPosition = position
	if !a.isExpanded {
		a.setWindowToSide()
	}
	utils.LogOperation("设置窗体位置", "系统用户", position)
}

// setWindowToSide 将窗体设置到屏幕边侧
func (a *App) setWindowToSide() {
	// 获取屏幕尺寸
	screens, err := runtime.ScreenGetAll(a.ctx)
	if err != nil || len(screens) == 0 {
		return
	}

	primaryScreen := screens[0]
	screenWidth := primaryScreen.Size.Width
	screenHeight := primaryScreen.Size.Height

	var x, y int
	if a.windowPosition == "right" {
		// 右侧位置
		x = screenWidth - 380 - 10 // 窗体宽度 + 边距
	} else {
		// 左侧位置（默认）
		x = 10
	}
	y = (screenHeight - 800) / 2 // 垂直居中

	runtime.WindowSetPosition(a.ctx, x, y)
}

// GetWindowState 获取当前窗体状态
func (a *App) GetWindowState() map[string]interface{} {
	return map[string]interface{}{
		"isExpanded": a.isExpanded,
		"position":   a.windowPosition,
	}
}

// MinimizeWindow 最小化窗体
func (a *App) MinimizeWindow() {
	runtime.WindowMinimise(a.ctx)
}

// SetAlwaysOnTop 设置窗体置顶状态
func (a *App) SetAlwaysOnTop(onTop bool) {
	if onTop {
		runtime.WindowSetAlwaysOnTop(a.ctx, true)
	} else {
		runtime.WindowSetAlwaysOnTop(a.ctx, false)
	}
	utils.LogOperation("设置窗体置顶", "系统用户", fmt.Sprintf("置顶: %v", onTop))
}

// HandleKeyboardShortcut 处理键盘快捷键
func (a *App) HandleKeyboardShortcut(key string) {
	switch key {
	case "F11":
		a.ToggleWindowSize()
	case "F10":
		// 切换位置
		if a.windowPosition == "left" {
			a.SetWindowPosition("right")
		} else {
			a.SetWindowPosition("left")
		}
	case "F9":
		a.MinimizeWindow()
	case "Ctrl+Shift+A":
		// 器官问题来源分析截图
		go func() {
			_, err := a.ProcessScreenshotAndUpload("器官问题来源分析", "医生或健康专家")
			if err != nil {
				utils.LogError("快捷键截图失败", "医生或健康专家", err)
			}
		}()
	case "Ctrl+Shift+B":
		// 生化平衡分析截图
		go func() {
			_, err := a.ProcessScreenshotAndUpload("生化平衡分析", "医生或健康专家")
			if err != nil {
				utils.LogError("快捷键截图失败", "医生或健康专家", err)
			}
		}()
	case "Ctrl+Shift+C":
		// 病理形态学分析截图
		go func() {
			_, err := a.ProcessScreenshotAndUpload("病理形态学分析", "医生或健康专家")
			if err != nil {
				utils.LogError("快捷键截图失败", "医生或健康专家", err)
			}
		}()
		// case "Ctrl+Shift+U":
		// 	// 上传最新截图 - 已注释，功能已由A、B、C快捷键的ProcessScreenshotAndUpload替代
		// 	go func() {
		// 		_, err := a.UploadLatestScreenshot()
		// 		if err != nil {
		// 			utils.LogError("快捷键上传失败", "医生或健康专家", err)
		// 		}
		// 	}()
	}
}

// AutoCollapseAfterInactivity 长时间无操作自动收缩
func (a *App) AutoCollapseAfterInactivity() {
	if a.isExpanded {
		a.ToggleWindowSize()
		utils.LogOperation("自动收缩窗体", "系统", "长时间无操作")
	}
}

// HandleHotkey 处理快捷键事件
func (a *App) HandleHotkey(hotkey string) {
	switch hotkey {
	case "Ctrl+X":
		// 退出程序
		runtime.Quit(a.ctx)
	case "Ctrl+Shift+A":
		// 器官问题来源分析截图
		go func() {
			_, err := a.ProcessScreenshotAndUpload("器官问题来源分析", "医生或健康专家")
			if err != nil {
				utils.LogError("快捷键截图失败", "医生或健康专家", err)
			}
		}()
	case "Ctrl+Shift+B":
		// 生化平衡分析截图
		go func() {
			_, err := a.ProcessScreenshotAndUpload("生化平衡分析", "医生或健康专家")
			if err != nil {
				utils.LogError("快捷键截图失败", "医生或健康专家", err)
			}
		}()
	case "Ctrl+Shift+C":
		// 病理形态学分析截图
		go func() {
			_, err := a.ProcessScreenshotAndUpload("病理形态学分析", "医生或健康专家")
			if err != nil {
				utils.LogError("快捷键截图失败", "医生或健康专家", err)
			}
		}()
		// case "Ctrl+Shift+U":
		// 	// 上传最新截图 - 已注释，功能已由A、B、C快捷键的ProcessScreenshotAndUpload替代
		// 	go func() {
		// 		_, err := a.UploadLatestScreenshot()
		// 		if err != nil {
		// 			utils.LogError("快捷键上传失败", "医生或健康专家", err)
		// 		}
		// 	}()
	}
}

// shutdown 应用关闭时的清理工作
func (a *App) shutdown(ctx context.Context) bool {
	utils.LogInfo("应用开始关闭，执行清理工作...")

	// 停止快捷键服务
	if a.hotkeyService != nil {
		a.hotkeyService.StopHotkeyListener()
		utils.LogInfo("快捷键服务已停止")
	}

	// 关闭OCR服务
	if a.ocrService != nil {
		a.ocrService.Close()
		utils.LogInfo("OCR服务已关闭")
	}

	utils.LogInfo("应用清理工作完成")

	// 确保进程完全退出
	go func() {
		time.Sleep(100 * time.Millisecond) // 给日志一点时间写入
		os.Exit(0)
	}()

	return false // 返回false表示允许关闭
}

// TestOCR 测试OCR功能
func (a *App) TestOCR(imagePath string) (map[string]interface{}, error) {
	utils.LogOperation("测试OCR功能", "系统用户", a.config.SiteInfo.SiteID)

	if a.ocrService == nil {
		return nil, fmt.Errorf("OCR服务未初始化")
	}

	// 验证OCR环境
	if err := a.ocrService.ValidateOCREnvironment(); err != nil {
		utils.LogError("OCR环境验证失败", "系统用户", err)
		return nil, fmt.Errorf("OCR环境验证失败: %v", err)
	}

	// 处理图片并获取详细结果
	result, err := a.ocrService.ProcessImageWithDetails(imagePath)
	if err != nil {
		utils.LogError("OCR处理失败", "系统用户", err)
		return nil, fmt.Errorf("OCR处理失败: %v", err)
	}

	utils.LogInfo(fmt.Sprintf("OCR处理成功 - 器官: %s, 置信度: %.2f", result.OrganName, result.Confidence))

	return map[string]interface{}{
		"full_text":    result.FullText,
		"organ_name":   result.OrganName,
		"confidence":   result.Confidence,
		"process_time": result.ProcessTime.Milliseconds(),
		"image_path":   result.ImagePath,
	}, nil
}

// ExtractOrganFromScreenshot 从截图中提取器官名称
func (a *App) ExtractOrganFromScreenshot(imagePath string) (string, error) {
	utils.LogOperation("提取器官名称", "系统用户", a.config.SiteInfo.SiteID)

	if a.ocrService == nil {
		return "", fmt.Errorf("OCR服务未初始化")
	}

	organName, err := a.ocrService.ExtractOrganName(imagePath)
	if err != nil {
		utils.LogError("提取器官名称失败", "系统用户", err)
		return "", err
	}

	utils.LogInfo(fmt.Sprintf("成功提取器官名称: %s", organName))
	return organName, nil
}

// ValidateOCRSetup 验证OCR环境设置
func (a *App) ValidateOCRSetup() error {
	utils.LogOperation("验证OCR环境", "系统用户", a.config.SiteInfo.SiteID)

	if a.ocrService == nil {
		return fmt.Errorf("OCR服务未初始化")
	}

	err := a.ocrService.ValidateOCREnvironment()
	if err != nil {
		utils.LogError("OCR环境验证失败", "系统用户", err)
	} else {
		utils.LogInfo("OCR环境验证成功")
	}

	return err
}

// TakeOCRScreenshot 专门用于OCR的截图方法
func (a *App) TakeOCRScreenshot(mode string, userName string) (map[string]interface{}, error) {
	utils.LogOperation("OCR专用截图", userName, a.config.SiteInfo.SiteID)

	// 使用OCR专用截图方法
	ocrFilePath, organName, err := a.screenshotService.TakeOCRRegionScreenshot(mode, userName)
	if err != nil {
		utils.LogError("OCR截图失败", userName, err)
		return nil, fmt.Errorf("OCR截图失败: %v", err)
	}

	utils.LogInfo(fmt.Sprintf("OCR截图成功 - 器官: %s", organName))

	return map[string]interface{}{
		"ocr_file_path": ocrFilePath,
		"organ_name":    organName,
		"mode":          mode,
		"user_name":     userName,
		"timestamp":     time.Now().Format("2006-01-02 15:04:05"),
	}, nil
}

// ProcessScreenshotWithOCR 截图并同步进行OCR识别
func (a *App) ProcessScreenshotWithOCR(mode string, userName string) (map[string]interface{}, error) {
	utils.LogOperation("截图+OCR处理", userName, a.config.SiteInfo.SiteID)

	// 1. 执行原有的截图和上传流程
	picURL, err := a.ProcessScreenshotAndUpload(mode, userName)
	if err != nil {
		return nil, fmt.Errorf("截图上传失败: %v", err)
	}

	// 2. 同步执行OCR识别
	ocrFilePath, organName, ocrErr := a.screenshotService.TakeOCRRegionScreenshot(mode, userName)
	if ocrErr != nil {
		// OCR失败不影响主流程，但记录错误
		utils.LogError("OCR识别失败", userName, ocrErr)
		organName = "未知器官"
		ocrFilePath = ""
	}

	return map[string]interface{}{
		"pic_url":       picURL,
		"ocr_file_path": ocrFilePath,
		"organ_name":    organName,
		"mode":          mode,
		"user_name":     userName,
		"timestamp":     time.Now().Format("2006-01-02 15:04:05"),
		"ocr_success":   ocrErr == nil,
	}, nil
}

// Greet returns a greeting for the given name
func (a *App) Greet(name string) string {
	return fmt.Sprintf("Hello %s, It's show time!", name)
}
